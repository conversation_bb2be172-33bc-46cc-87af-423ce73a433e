import { sql } from "drizzle-orm";
import { type NextRequest, NextResponse } from "next/server";
import type { CreateBodyDataRequest } from "~/lib/interfaces/keypoint";
import { BodyDataSchema } from "~/server/api/utils/apiInputs";
import { returnError } from "~/server/api/utils/returnError";
import { db } from "~/server/db";
import { bodyAngles, bodyKeypoints } from "~/server/db/schema";
import { getPoseDataWithBuffering } from "~/server/api/utils/poseData";
import { calculateAnglesFromKeypoints } from "~/lib/poseRenderer";
import type { PartialBodyKeypoints } from "~/lib/interfaces/drawingTypes";

export const maxDuration = 300;

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const poseData = await getPoseDataWithBuffering(params.id);

    return NextResponse.json(poseData);
  } catch (error) {
    return returnError(error);
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const body = (await request.json()) as CreateBodyDataRequest;

    const result = BodyDataSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: result.error.format(),
        },
        { status: 400 },
      );
    }

    const validatedData = result.data;
    const angles = validatedData.angles;

    if (angles && angles.length > 0) {
      await db
        .insert(bodyAngles)
        .values(angles.map((x) => ({ ...x, videoId: params.id })))
        .onDuplicateKeyUpdate({
          set: {
            angle: sql`values(${bodyAngles.angle})`,
            aiScore: sql`values(${bodyAngles.aiScore})`,
          },
        });
    }
    console.log("angles inserted");

    const keypoints = validatedData.keypoints;
    if (keypoints && keypoints.length > 0) {
      await db
        .insert(bodyKeypoints)
        .values(keypoints.map((x) => ({ ...x, videoId: params.id })))
        .onDuplicateKeyUpdate({
          set: {
            x: sql`values(${bodyKeypoints.x})`,
            y: sql`values(${bodyKeypoints.y})`,
            z: sql`values(${bodyKeypoints.z})`,
            aiScore: sql`values(${bodyKeypoints.aiScore})`,
          },
        });
    }
    console.log("keypoints inserted");

    return NextResponse.json(
      {
        message: "Body data created successfully",
      },
      { status: 201 },
    );
  } catch (error) {
    return returnError(error);
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const body = (await request.json()) as {
      frameNumber: number;
      keypoints: Array<{
        keypointNum: number;
        x: number;
        y: number;
        z?: number;
      }>;
    };

    const { frameNumber, keypoints } = body;
    const videoId = params.id;

    // Update keypoints in database
    for (const keypoint of keypoints) {
      await db
        .insert(bodyKeypoints)
        .values({
          videoId,
          frameNumber,
          keypointNum: keypoint.keypointNum,
          x: keypoint.x,
          y: keypoint.y,
          z: keypoint.z ?? 0,
          aiScore: 1.0, // Mark as manually edited
        })
        .onDuplicateKeyUpdate({
          set: {
            x: sql`values(${bodyKeypoints.x})`,
            y: sql`values(${bodyKeypoints.y})`,
            z: sql`values(${bodyKeypoints.z})`,
            aiScore: sql`values(${bodyKeypoints.aiScore})`,
          },
        });
    }

    // Recalculate angles from updated keypoints
    const updatedKeypoints: PartialBodyKeypoints = keypoints.map((kp) => ({
      frameNumber,
      keypointNum: kp.keypointNum,
      x: kp.x,
      y: kp.y,
    }));

    const recalculatedAngles = calculateAnglesFromKeypoints(
      updatedKeypoints,
      frameNumber,
    );

    // Update angles in database
    for (const angle of recalculatedAngles) {
      await db
        .insert(bodyAngles)
        .values({
          videoId,
          frameNumber: angle.frameNumber,
          name: angle.name,
          angle: angle.angle,
          aiScore: angle.aiScore,
        })
        .onDuplicateKeyUpdate({
          set: {
            angle: sql`values(${bodyAngles.angle})`,
            aiScore: sql`values(${bodyAngles.aiScore})`,
          },
        });
    }

    return NextResponse.json(
      {
        message: "Pose data updated successfully",
        updatedKeypoints: keypoints.length,
        recalculatedAngles: recalculatedAngles.length,
      },
      { status: 200 },
    );
  } catch (error) {
    return returnError(error);
  }
}
