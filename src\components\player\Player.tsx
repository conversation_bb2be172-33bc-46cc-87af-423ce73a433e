"use client";

import { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import { useHotkeys } from "react-hotkeys-hook";
import videojs from "video.js";
import type Player from "video.js/dist/types/player";
import "video.js/dist/video-js.css";
import { Card } from "~/components/Card";
import { Input } from "~/components/ui/input";
import { Progress } from "~/components/ui/progress";
import { View } from "~/components/View";
import { useStore } from "~/hooks/store";
import { useRunOnce } from "~/hooks/useEffectOnce";
import { extractFrames, type TSSegment } from "~/lib/ffmpeg";
import { getSegmentsByFrameNumber } from "~/lib/m3u8";
import { PlayerButtons } from "./PlayerButtons";
import { VideoTimeline } from "./VideoTimeline";
import { cn, getNextTag, getPrevTag } from "~/lib/utils";
import { FpsEditor } from "./FpsEditor";
import type { PoseDataWithBuffering } from "~/server/api/utils/poseData";
import type { M3u8Segment, TagUI } from "~/lib/interface";
import type {
  PartialBodyKeypoints,
  PartialBodyAngles,
  ColouredLine,
  DrawingMode,
  LineColor,
  PoseFrameMap,
  PoseEditingState,
  SelectedKeypoint,
  EditableKeypoint,
} from "~/lib/interfaces/drawingTypes";
import { CanvasManager } from "./CanvasManager";

import { PoseControlMenu } from "./PoseDropdown";
import { ConfirmationModal } from "../ui/ConfirmationModal";

export const VideoPlayer = ({
  m3u8Segments,
  m3u8Text,
  tagTypes,
  poseData,
}: {
  m3u8Segments: M3u8Segment[];
  m3u8Text: string;
  tagTypes: TagUI[];
  poseData: PoseDataWithBuffering;
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const playerRef = useRef<Player | null>(null);
  const frameInputRef = useRef<HTMLInputElement>(null);
  const playerContainerRef = useRef<HTMLDivElement>(null);
  const animationFrameId = useRef<number | null>(null);

  const currentFrame = useStore((state) => state.currentFrame);
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const filteredTags = useStore((state) => state.filteredTags);
  const editingTagId = useStore((state) => state.editingTagId);
  const videoSummary = useStore((state) => state.videoSummary);
  const setCurrentFrame = useStore((state) => state.setCurrentFrame);
  const setEditingTagId = useStore((state) => state.setEditingTagId);
  const [isTimelineVisible, setTimelineVisible] = useState(true);

  const [isCtrlPressed, setIsCtrlPressed] = useState(false);
  const [firstSegment, setFirstSegment] = useState<TSSegment>();
  const [duration, setDuration] = useState(0);
  const [isReading, setIsReading] = useState(false);
  const [playerSize, setPlayerSize] = useState<{
    width: number;
    height: number;
  }>({ width: 0, height: 0 });
  const [videoAspectRatio, setVideoAspectRatio] = useState(16 / 9);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showPose, setShowPose] = useState(true);
  const [showAngles, setShowAngles] = useState(true);
  const [showVideo, setShowVideo] = useState(true);
  const [selectedColour, setSelectedColour] = useState<LineColor>("red");
  const [drawingMode, setDrawingMode] = useState<DrawingMode>("line");
  const [lines, setLines] = useState<ColouredLine[]>([]);
  const [hotkeyCd, setHotkeyCd] = useState(false);
  const [poseDataByFrame, setPoseDataByFrame] = useState<PoseFrameMap>({
    keypoints: new Map<number, PartialBodyKeypoints>(),
    angles: new Map<number, PartialBodyAngles>(),
  });
  const [strokeWidth, setStrokeWidth] = useState<number>(2);
  const [poseCircleThickness, setPoseCircleThickness] = useState<number>(4);
  const [poseLineThickness, setPoseLineThickness] = useState<number>(2);

  const [isZoomMode, setIsZoomMode] = useState(false);

  // Pose editing state
  const [poseEditingState, setPoseEditingState] = useState<PoseEditingState>({
    isPoseEditMode: false,
    selectedKeypoint: null,
    isDragging: false,
    hasUnsavedChanges: false,
  });

  // Modified keypoints for current editing session
  const [modifiedKeypoints, setModifiedKeypoints] = useState<
    Map<string, EditableKeypoint>
  >(new Map());

  // Confirmation modal state
  const [showExitConfirmModal, setShowExitConfirmModal] = useState(false);
  const [zoomLevel, setZoomLevel] = useState<number>(1.5);
  const [zoomState, setZoomState] = useState({
    scale: 1,
    offsetX: 0,
    offsetY: 0,
  });

  const nextTag = getNextTag(currentFrame, filteredTags, editingTagId);
  const prevTag = getPrevTag(currentFrame, filteredTags, editingTagId);

  const fps = firstSegment?.fps;
  const totalFrames = duration * (fps ?? 0);

  const handleUndoLine = () => {
    setLines((x) => x.slice(0, -1));
  };

  const handleClearLines = () => {
    setLines([]);
  };

  // Pose editing control functions
  const enterPoseEditMode = () => {
    // Pause the video when entering edit mode
    const player = playerRef.current;
    if (player && !player.paused()) {
      player.pause();
    }

    setPoseEditingState((prev) => ({
      ...prev,
      isPoseEditMode: true,
      hasUnsavedChanges: false,
    }));
  };

  const exitPoseEditMode = () => {
    setPoseEditingState({
      isPoseEditMode: false,
      selectedKeypoint: null,
      isDragging: false,
      hasUnsavedChanges: false,
    });
    setModifiedKeypoints(new Map());
  };

  const togglePoseEditMode = () => {
    if (poseEditingState.isPoseEditMode) {
      if (poseEditingState.hasUnsavedChanges) {
        // Show confirmation modal for unsaved changes
        setShowExitConfirmModal(true);
        return;
      }
      exitPoseEditMode();
    } else {
      enterPoseEditMode();
    }
  };

  const handleConfirmExit = () => {
    exitPoseEditMode();
  };

  const savePoseEdits = async () => {
    if (!videoSummary?.id || modifiedKeypoints.size === 0) return;

    try {
      // Group modified keypoints by frame
      const keypointsByFrame = new Map<
        number,
        Array<{
          keypointNum: number;
          x: number;
          y: number;
          z?: number;
        }>
      >();

      for (const keypoint of modifiedKeypoints.values()) {
        if (!keypointsByFrame.has(keypoint.frameNumber)) {
          keypointsByFrame.set(keypoint.frameNumber, []);
        }
        keypointsByFrame.get(keypoint.frameNumber)!.push({
          keypointNum: keypoint.keypointNum,
          x: keypoint.x,
          y: keypoint.y,
          z: 0, // Default z value
        });
      }

      // Save each frame's modifications
      for (const [frameNumber, keypoints] of keypointsByFrame) {
        const response = await fetch(`/api/v1/keypoints/${videoSummary.id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            frameNumber,
            keypoints,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to save pose data for frame ${frameNumber}`);
        }
      }

      // Clear modifications and update state
      setModifiedKeypoints(new Map());
      setPoseEditingState((prev) => ({
        ...prev,
        hasUnsavedChanges: false,
      }));

      toast.success("Pose edits saved successfully!");

      // Optionally refresh pose data
      // You might want to refetch the pose data here to get the updated angles
    } catch (error) {
      console.error("Error saving pose edits:", error);
      toast.error("Failed to save pose edits");
    }
  };

  useEffect(() => {
    if (hotkeyCd) {
      setTimeout(() => {
        setHotkeyCd(false);
      }, 10);
    }
  }, [hotkeyCd]);

  // Process pose data into frame-indexed lookup maps when it loads
  useEffect(() => {
    if (poseData) {
      // Create frame-indexed maps for fast lookup
      const keypointsByFrame = new Map<number, PartialBodyKeypoints>();
      const anglesByFrame = new Map<number, PartialBodyAngles>();

      // Process throws data - flatten all keypoints and angles from all throws
      poseData.throws.forEach((throwData) => {
        // Index keypoints by frame number
        throwData.keypoints.forEach((keypoint) => {
          if (!keypointsByFrame.has(keypoint.frameNumber)) {
            keypointsByFrame.set(keypoint.frameNumber, []);
          }
          keypointsByFrame.get(keypoint.frameNumber)!.push(keypoint);
        });

        // Index angles by frame number
        throwData.angles.forEach((angle) => {
          if (!anglesByFrame.has(angle.frameNumber)) {
            anglesByFrame.set(angle.frameNumber, []);
          }
          anglesByFrame.get(angle.frameNumber)!.push(angle);
        });
      });

      // Set the indexed data structure
      setPoseDataByFrame({
        keypoints: keypointsByFrame,
        angles: anglesByFrame,
      });
    }
  }, [poseData]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Control") {
        setIsCtrlPressed(true);
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === "Control") {
        setIsCtrlPressed(false);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, []);

  const currentFramePose = {
    keypoints: poseDataByFrame.keypoints.get(currentFrame) ?? [],
    angles: poseDataByFrame.angles.get(currentFrame) ?? [],
  };

  // Update player size on resize
  useEffect(() => {
    if (!playerContainerRef.current) return;

    const updateSize = () => {
      if (!playerContainerRef.current || isFullscreen) return;
      setPlayerSize({
        width: playerContainerRef.current.clientWidth,
        height: playerContainerRef.current.clientHeight,
      });
    };

    updateSize();
  }, [isFullscreen]);

  const {
    handleMouseDown: handleCanvasMouseDown,
    handleMouseMove: handleCanvasMouseMove,
    handleMouseUp: handleCanvasMouseUp,
  } = CanvasManager({
    canvasRef,
    currentFrame,
    filteredTags,
    selectedAthlete: selectedAthlete ?? "",
    showPose,
    showAngles,
    currentFramePose,
    firstSegment,
    lines,
    setLines,
    drawingMode,
    selectedColour,
    isCtrlPressed,
    strokeWidth,
    poseCircleThickness,
    poseLineThickness,
    poseEditingState,
    setPoseEditingState,
    modifiedKeypoints,
    setModifiedKeypoints,
  });

  // Mouse event handlers for drawing
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    handleCanvasMouseDown(e);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    handleCanvasMouseMove(e);
  };

  const handleMouseUp = () => {
    handleCanvasMouseUp();
  };

  // New zoom functionality
  const handleVideoClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isZoomMode) return;

    // Prevent default behavior
    e.preventDefault();
    e.stopPropagation();

    const overlay = e.currentTarget;
    const rect = overlay.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const clickY = e.clientY - rect.top;

    // Toggle between current scale and selected zoom level
    const newScale = zoomState.scale === 1 ? zoomLevel : 1;

    if (newScale === 1) {
      // Reset zoom
      setZoomState({ scale: 1, offsetX: 0, offsetY: 0 });
    } else {
      // Zoom in at click position
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const offsetX = (clickX - centerX) * (1 - newScale);
      const offsetY = (clickY - centerY) * (1 - newScale);

      setZoomState({ scale: newScale, offsetX, offsetY });
    }

    // Exit zoom mode after clicking
    setIsZoomMode(false);
  };

  const toggleZoomMode = () => {
    setIsZoomMode(!isZoomMode);
  };

  // Reset zoom state when zoom level changes
  useEffect(() => {
    setZoomState({ scale: 1, offsetX: 0, offsetY: 0 });
  }, [zoomLevel]);

  const getVideoTransformStyle = (): React.CSSProperties => {
    return {
      transform: `translate(${zoomState.offsetX}px, ${zoomState.offsetY}px) scale(${zoomState.scale})`,
      transformOrigin: "center center",
      cursor: isZoomMode ? "zoom-in" : "default",
    };
  };

  //load first ts chunk
  useRunOnce(() => {
    //get info of first segment
    const getFirstSegmentInfo = async () => {
      try {
        const { forwardSegments } = getSegmentsByFrameNumber({
          segments: m3u8Segments,
          frameNumber: currentFrame,
          fps: fps ?? 50,
        });
        setIsReading(true);
        const segment = forwardSegments[0]!;
        const frames = await extractFrames({
          videoUrl: segment.uri,
        });

        setFirstSegment(frames);
        setIsReading(false);
      } catch (error) {
        console.error(error);
        toast.error("Failed to load video segment");
      }
    };
    void getFirstSegmentInfo();
  });

  //load m3u8 for video js
  useRunOnce(() => {
    if (!videoRef.current) return;

    const blob = new Blob([m3u8Text], { type: "application/x-mpegURL" });
    const url = URL.createObjectURL(blob);

    const options = {
      autoplay: false,
      bigPlayButton: false,
      preload: "auto",
      // Disable all progress and seeking controls
      controlBar: {
        progressControl: false,
        playToggle: true,
        volumePanel: true,
        fullscreenToggle: true,
        remainingTimeDisplay: true,
        currentTimeDisplay: true,
      },
      // muted: true,
      // controls: !isReading,
      controls: true,
      responsive: true,
      fluid: true,
      sources: [
        {
          src: url,
          type: "application/x-mpegURL",
        },
      ],
    };

    let player: Player;

    if (!playerRef.current) {
      player = videojs(videoRef.current, options, () => {
        player.on("fullscreenchange", () => {
          // VideoJS has its own fullscreen API, this ensures we catch those events too
          const isPlayerFullscreen = player.isFullscreen() ?? false;
          setIsFullscreen(isPlayerFullscreen);
        });

        player.on("dispose", () => {
          videojs.log("player will dispose");
        });
      });

      player.on("error", () => {
        const error = player.error();
        console.error("Video.js Error:", error);
      });

      playerRef.current = player;
    } else {
      player = playerRef.current;
      player.src(url);
    }
  });

  const onVideoTimeUpdate = () => {
    if (!firstSegment) return;
    const video = videoRef.current;
    if (!video) return;

    const frameNumber = video.currentTime * (fps ?? 0) + 1;
    const frameNumberRounded = Math.floor(
      Math.round(frameNumber * 1000) / 1000,
    );
    if (!video.paused) {
      setCurrentFrame(frameNumberRounded);
    }
  };

  const startFrameUpdates = () => {
    if (!videoRef.current || !firstSegment) return;

    const updateFrame = () => {
      // Only update if the video is playing
      if (videoRef.current && !videoRef.current.paused) {
        onVideoTimeUpdate();
        animationFrameId.current = requestAnimationFrame(updateFrame);
      } else {
        stopFrameUpdates();
      }
    };

    animationFrameId.current = requestAnimationFrame(updateFrame);
  };

  const stopFrameUpdates = () => {
    if (animationFrameId.current !== null) {
      cancelAnimationFrame(animationFrameId.current);
      animationFrameId.current = null;
    }
  };

  useEffect(() => {
    const player = playerRef.current;
    if (!player) return;

    const handlePlay = () => {
      startFrameUpdates();
    };

    const handlePause = () => {
      stopFrameUpdates();
      // This ensures we get the final frame
      setTimeout(() => {
        onVideoTimeUpdate();
      }, 50);
    };

    player.on("play", handlePlay);
    player.on("pause", handlePause);

    // Start updates if video is already playing
    if (player.paused() === false) {
      startFrameUpdates();
    }

    return () => {
      player.off("play", handlePlay);
      player.off("pause", handlePause);
      stopFrameUpdates();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [firstSegment]);

  //when video time updates, update current frame

  const playerElement = playerRef.current;

  const onFrameNumberChange = (value: string) => {
    if (!currentFrame || !fps) return;
    const video = videoRef.current;
    if (!video) return;
    const newFrame = Math.round(+value);
    setCurrentFrame(newFrame);

    if (video.paused) {
      let newCurrentTime = (newFrame - 1) / fps;
      newCurrentTime += 0.00001;
      video.currentTime = newCurrentTime;
    }
  };

  const onNextFrame = (number = 1) => {
    playerElement?.pause();
    onFrameNumberChange((currentFrame + number).toString());
  };

  const onPrevFrame = (number = 1) => {
    if (currentFrame <= 1) return;
    playerElement?.pause();
    let targetFrame = currentFrame - number;
    if (targetFrame < 1) targetFrame = 1;
    onFrameNumberChange(targetFrame.toString());
  };

  const onNextTag = () => {
    if (!nextTag) return;
    if (!playerElement?.paused()) {
      playerElement?.pause();
    }
    onFrameNumberChange(nextTag.frame.toString());
    setEditingTagId(nextTag.id);
  };

  const onPrevTag = () => {
    if (!prevTag) return;
    playerElement?.pause();
    onFrameNumberChange(prevTag.frame.toString());
    setEditingTagId(prevTag.id);
  };

  const onPlayPauseVideo = () => {
    if (isReading) return;
    const player = playerRef.current;
    if (!player) return;
    if (player.paused()) {
      void player.play();
    } else {
      player.pause();
    }
  };

  const handleProgressBarClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const progressBar = event.currentTarget;
    const clickPosition =
      event.clientX - progressBar.getBoundingClientRect().left;
    const progressBarWidth = progressBar.offsetWidth;
    const percentage = clickPosition / progressBarWidth;
    const player = playerRef.current;
    if (player && duration && fps) {
      const newTime = percentage * duration;
      player.currentTime(newTime);

      // updating currentFrame here as well as currenttime
      const newFrame = Math.floor(newTime * fps) + 1;
      setCurrentFrame(newFrame);
    }
  };

  // Calculate dimensions to fit video in container while preserving aspect ratio
  const calculatePlayerDimensions = () => {
    const containerWidth = playerSize.width;
    const containerHeight = playerSize.height;
    // If container is wider than video aspect ratio
    if (containerWidth / containerHeight > videoAspectRatio) {
      // Height is limiting factor
      const height = containerHeight;
      const width = height * videoAspectRatio;
      return { width, height };
    } else {
      // Width is limiting factor
      const width = containerWidth;
      const height = width / videoAspectRatio;
      return { width, height };
    }
  };

  const playerDimensions = calculatePlayerDimensions();

  const onHotKeyPress = (func: () => void) => {
    if (hotkeyCd) return;
    setHotkeyCd(true);
    func();
  };

  useHotkeys("left", () => onHotKeyPress(onPrevFrame));
  useHotkeys("shift+left", () => onHotKeyPress(() => onPrevFrame(10)));
  useHotkeys("ctrl+left", () => onHotKeyPress(onPrevTag));
  useHotkeys("right", () => onHotKeyPress(onNextFrame));
  useHotkeys("shift+right", () => onHotKeyPress(() => onNextFrame(10)));
  useHotkeys("ctrl+right", () => onHotKeyPress(onNextTag));
  useHotkeys("esc", () => {
    setEditingTagId(null);
    // Also exit pose edit mode on escape
    if (poseEditingState.isPoseEditMode) {
      exitPoseEditMode();
    }
  });
  useHotkeys("space", () => onPlayPauseVideo());
  useHotkeys("ctrl+z", () => handleUndoLine());
  useHotkeys("z", () => toggleZoomMode());
  useHotkeys("e", () => togglePoseEditMode()); // Toggle pose edit mode with 'E' key

  useHotkeys(
    "ctrl+q",
    () => {
      setTimelineVisible(!isTimelineVisible);
    },
    [isTimelineVisible],
  );

  useEffect(() => {
    return () => {
      stopFrameUpdates();
    };
  }, []);

  return (
    <div className="col-span-9 flex h-full flex-col gap-2.5">
      <Card className="relative flex min-h-0 flex-1 flex-col gap-[5px]">
        <div
          ref={playerContainerRef}
          className={cn(
            "flex min-h-0 flex-1 items-center justify-center overflow-hidden bg-seaSalt-60",
            isTimelineVisible ? "" : "h-[calc(100%+166px)]",
          )}
        >
          <div
            className={cn(
              "relative max-h-full max-w-full",
              !isTimelineVisible && "w-full",
            )}
            style={{
              width: isTimelineVisible ? playerDimensions.width : undefined,
              height: isTimelineVisible ? playerDimensions.height : undefined,
            }}
          >
            <video
              id="video-player"
              ref={videoRef}
              style={getVideoTransformStyle()}
              onLoadedMetadata={(e) => {
                setDuration(e.currentTarget.duration);
                if (e.currentTarget.videoWidth && e.currentTarget.videoHeight) {
                  setVideoAspectRatio(
                    e.currentTarget.videoWidth / e.currentTarget.videoHeight,
                  );
                }
              }}
              className="video-js h-full w-full"
              playsInline
              onTimeUpdate={onVideoTimeUpdate}
              onPause={() => {
                //This is a bit hacky, do this because setState is fake sync. We want to call nextFrame after state is updated
                setTimeout(() => {
                  onNextFrame();
                }, 300);
              }}
            />
            {!showVideo && (
              <div
                className="z-5 absolute inset-0 bg-black"
                style={getVideoTransformStyle()}
              />
            )}
            {isZoomMode && (
              <div
                className="absolute inset-0 z-10 cursor-zoom-in"
                onClick={handleVideoClick}
              />
            )}
            <canvas
              ref={canvasRef}
              className={cn(
                "absolute left-0 top-0 z-10 h-full w-full",
                isCtrlPressed || poseEditingState.isPoseEditMode
                  ? "pointer-events-auto"
                  : "pointer-events-none",
              )}
              style={getVideoTransformStyle()}
              width={firstSegment?.width ?? playerDimensions.width}
              height={firstSegment?.height ?? playerDimensions.height}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            />
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex gap-5 text-smallLabel">
            <div className="grid grid-cols-2 gap-x-[5px] gap-y-1">
              <p>Current frame:</p>
              <p>{currentFrame}</p>
              <p>Current time:</p>
              <p>{((currentFrame - 1) / (fps ?? 1)).toFixed(3)}</p>
              {currentFramePose.keypoints.length > 0 && (
                <>
                  <p>Keypoints:</p>
                  <p>{currentFramePose.keypoints.length}</p>
                </>
              )}
            </div>
            {fps && <FpsEditor defaultFps={videoSummary?.fps ?? fps} />}
          </div>
          <PlayerButtons
            currentFrame={currentFrame}
            isReading={isReading}
            isPaused={playerRef.current?.paused()}
            onPlayPauseVideo={onPlayPauseVideo}
            onPrevFrame={onPrevFrame}
            onNextFrame={onNextFrame}
            isZoomMode={isZoomMode}
            onToggleZoomMode={toggleZoomMode}
          />

          <div className="flex flex-col gap-2.5">
            <div className="flex items-center gap-[2.5px]">
              <View
                hotkeys={["Ctrl", "→"]}
                description="next tag"
                className="w-24 translate-x-[-50%] translate-y-[50%]"
              >
                <p className="text-smallLabel">Go to frame:</p>
              </View>
              <View
                hotkeys={["Ctrl", "→"]}
                description="next tag"
                className="w-24 translate-x-[-20%] translate-y-[50%]"
              >
                <Input
                  placeholder="e.g. 200"
                  ref={frameInputRef}
                  disabled={isReading}
                  type="number"
                  className="text-smallLabel placeholder:text-smallLabel"
                  onFinish={(value) => {
                    onFrameNumberChange(value);
                    frameInputRef.current?.blur();
                  }}
                />
              </View>
            </div>
            <PoseControlMenu
              setShowPose={setShowPose}
              setShowAngles={setShowAngles}
              setShowVideo={setShowVideo}
              showPose={showPose}
              showAngles={showAngles}
              showVideo={showVideo}
              handleUndoLine={handleUndoLine}
              handleClearLines={handleClearLines}
              selectedColour={selectedColour}
              setSelectedColour={setSelectedColour}
              drawingMode={drawingMode}
              setDrawingMode={setDrawingMode}
              strokeWidth={strokeWidth}
              setStrokeWidth={setStrokeWidth}
              poseCircleThickness={poseCircleThickness}
              setPoseCircleThickness={setPoseCircleThickness}
              poseLineThickness={poseLineThickness}
              setPoseLineThickness={setPoseLineThickness}
              zoomLevel={zoomLevel}
              setZoomLevel={setZoomLevel}
              isPoseEditMode={poseEditingState.isPoseEditMode}
              hasUnsavedChanges={poseEditingState.hasUnsavedChanges}
              onTogglePoseEditMode={togglePoseEditMode}
              onSavePoseEdits={savePoseEdits}
            />
          </div>
        </div>
        <Progress
          value={(currentFrame / totalFrames) * 100}
          onClick={isZoomMode ? undefined : handleProgressBarClick}
          className={isZoomMode ? "cursor-default" : "cursor-pointer"}
        />
      </Card>

      {isTimelineVisible && (
        <div className="h-[166px] shrink-0">
          <VideoTimeline
            tagTypes={tagTypes}
            totalFrames={totalFrames}
            onFrameNumberChange={onFrameNumberChange}
            setEditingTagId={setEditingTagId}
          />
        </div>
      )}

      <ConfirmationModal
        isOpen={showExitConfirmModal}
        onClose={() => setShowExitConfirmModal(false)}
        onConfirm={handleConfirmExit}
        title="Exit Pose Editing"
        description="You have unsaved pose changes. Are you sure you want to exit editing mode? All changes will be lost."
        confirmText="Exit Without Saving"
        cancelText="Stay in Edit Mode"
        variant="destructive"
      />
    </div>
  );
};
