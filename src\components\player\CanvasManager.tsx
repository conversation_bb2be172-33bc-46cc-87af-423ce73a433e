/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import type React from "react";

import { useState, useRef, useEffect } from "react";
import {
  drawCurrentLine,
  drawLines,
  drawTagBoundingBox,
} from "~/lib/drawingUtilts";
import type {
  CanvasManagerHandlers,
  CanvasManagerProps,
} from "~/lib/interfaces/canvasTypes";
import { type CurrentLine } from "~/lib/interfaces/drawingTypes";

import { drawPoseOverlay, findClickedKeypoint } from "~/lib/poseRenderer";

export function CanvasManager({
  canvasRef,
  currentFrame,
  filteredTags,
  selectedAthlete,
  showPose,
  showAngles,
  currentFramePose,
  firstSegment,
  lines,
  setLines,
  drawingMode,
  selectedColour,
  isCtrlPressed,
  strokeWidth = 2,
  poseCircleThickness = 4,
  poseLineThickness = 2,
  poseEditingState,
  setPoseEditingState,
  modifiedKeypoints,
  setModifiedKeypoints,
}: CanvasManagerProps): CanvasManagerHandlers {
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentLine, setCurrentLine] = useState<CurrentLine | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  // Double-click detection for keypoint reset
  const [lastClickTime, setLastClickTime] = useState<number>(0);
  const [lastClickedKeypoint, setLastClickedKeypoint] = useState<number | null>(
    null,
  );
  const DOUBLE_CLICK_THRESHOLD = 400; // milliseconds

  // Reset keypoint to original position
  const resetKeypoint = (keypointNum: number, frameNumber: number) => {
    const keypointKey = `${frameNumber}-${keypointNum}`;

    // Remove from modified keypoints map to restore original position
    setModifiedKeypoints((prev) => {
      const newMap = new Map(prev);
      newMap.delete(keypointKey);
      return newMap;
    });

    // Mark as having changes (removing a modification is still a change)
    setPoseEditingState((prev) => ({
      ...prev,
      hasUnsavedChanges: true,
    }));
  };

  // Reset double-click tracking when frame changes or edit mode exits
  useEffect(() => {
    setLastClickTime(0);
    setLastClickedKeypoint(null);
  }, [currentFrame, poseEditingState.isPoseEditMode]);

  // Handle mouse interactions (drawing or pose editing)
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current!;
    const rect = canvas.getBoundingClientRect();

    // Normalize mouse coordinates (0 to 1)
    const x = (e.clientX - rect.left) / rect.width;
    const y = (e.clientY - rect.top) / rect.height;

    // Handle pose editing mode
    if (poseEditingState.isPoseEditMode && showPose) {
      const ffmpegWidth = firstSegment?.width;
      const ffmpegHeight = firstSegment?.height;
      const videoElement = videoRef.current;
      const videoWidth =
        ffmpegWidth ?? videoElement?.videoWidth ?? canvas.width;
      const videoHeight =
        ffmpegHeight ?? videoElement?.videoHeight ?? canvas.height;

      // Get current keypoints (including any modifications)
      let currentKeypoints = currentFramePose.keypoints;

      // Apply any modified keypoints for this frame
      const modifiedForFrame = Array.from(modifiedKeypoints.values()).filter(
        (kp) => kp.frameNumber === currentFrame,
      );

      if (modifiedForFrame.length > 0) {
        currentKeypoints = currentKeypoints.map((kp) => {
          const modified = modifiedForFrame.find(
            (mkp) => mkp.keypointNum === kp.keypointNum,
          );
          return modified ? { ...kp, x: modified.x, y: modified.y } : kp;
        });
      }

      const clickedKeypoint = findClickedKeypoint(
        x,
        y,
        currentKeypoints,
        poseCircleThickness,
        canvas.width,
        canvas.height,
        videoWidth,
        videoHeight,
      );

      if (clickedKeypoint) {
        const currentTime = Date.now();
        const timeSinceLastClick = currentTime - lastClickTime;

        // Check for double-click on the same keypoint
        if (
          timeSinceLastClick < DOUBLE_CLICK_THRESHOLD &&
          lastClickedKeypoint === clickedKeypoint.keypointNum
        ) {
          // Double-click detected - reset keypoint to original position
          resetKeypoint(clickedKeypoint.keypointNum, currentFrame);

          // Reset double-click tracking
          setLastClickTime(0);
          setLastClickedKeypoint(null);

          console.log(
            `Reset keypoint ${clickedKeypoint.keypointNum} to original position`,
          );
          return;
        }

        // Single click - start dragging
        setLastClickTime(currentTime);
        setLastClickedKeypoint(clickedKeypoint.keypointNum);

        setPoseEditingState((prev) => ({
          ...prev,
          selectedKeypoint: {
            keypointNum: clickedKeypoint.keypointNum,
            frameNumber: currentFrame,
            originalX: clickedKeypoint.x,
            originalY: clickedKeypoint.y,
          },
          isDragging: true,
        }));
        return;
      }
    }

    // Handle drawing mode (only if Ctrl is pressed and not in pose edit mode)
    if (isCtrlPressed && !poseEditingState.isPoseEditMode) {
      setIsDrawing(true);
      setCurrentLine({
        startX: x,
        startY: y,
        endX: x,
        endY: y,
        strokeWidth,
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current!;
    const rect = canvas.getBoundingClientRect();

    // Normalize mouse coordinates (0 to 1)
    const x = (e.clientX - rect.left) / rect.width;
    const y = (e.clientY - rect.top) / rect.height;

    // Handle pose editing drag
    if (poseEditingState.isDragging && poseEditingState.selectedKeypoint) {
      const selectedKp = poseEditingState.selectedKeypoint;

      // Convert normalized coordinates to appropriate format
      // If original coordinates were normalized (0-1), keep them normalized
      // If they were in pixel coordinates, convert accordingly
      let newX = x;
      let newY = y;

      if (selectedKp.originalX > 1 || selectedKp.originalY > 1) {
        // Original was in pixel coordinates, convert from normalized
        const ffmpegWidth = firstSegment?.width;
        const ffmpegHeight = firstSegment?.height;
        const videoElement = videoRef.current;
        const videoWidth =
          ffmpegWidth ?? videoElement?.videoWidth ?? canvas.width;
        const videoHeight =
          ffmpegHeight ?? videoElement?.videoHeight ?? canvas.height;

        newX = x * videoWidth;
        newY = y * videoHeight;
      }

      // Update the modified keypoints map
      const keypointKey = `${selectedKp.frameNumber}-${selectedKp.keypointNum}`;
      setModifiedKeypoints((prev) => {
        const newMap = new Map(prev);
        newMap.set(keypointKey, {
          frameNumber: selectedKp.frameNumber,
          keypointNum: selectedKp.keypointNum,
          x: newX,
          y: newY,
          isModified: true,
        });
        return newMap;
      });

      // Mark as having unsaved changes
      setPoseEditingState((prev) => ({
        ...prev,
        hasUnsavedChanges: true,
      }));

      return;
    }

    // Handle drawing mode
    if (
      isDrawing &&
      isCtrlPressed &&
      currentLine &&
      !poseEditingState.isPoseEditMode
    ) {
      setCurrentLine({
        ...currentLine,
        endX: x,
        endY: y,
        strokeWidth,
      });
    }
  };

  const handleMouseUp = () => {
    // Handle pose editing drag end
    if (poseEditingState.isDragging) {
      setPoseEditingState((prev) => ({
        ...prev,
        isDragging: false,
        selectedKeypoint: null,
      }));
      return;
    }

    // Handle drawing mode
    if (isDrawing && currentLine) {
      setLines([
        ...lines,
        {
          ...currentLine,
          color: selectedColour,
          strokeWidth: currentLine.strokeWidth ?? strokeWidth,
        },
      ]);
      setCurrentLine(null);
    }
    setIsDrawing(false);
  };

  // Draw on canvas
  useEffect(() => {
    const canvas = canvasRef.current!;
    const ctx = canvas.getContext("2d")!;
    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;

    // Get the display dimensions (the actual size the canvas is rendered at in the DOM)
    const displayWidth = canvas.clientWidth;
    const displayHeight = canvas.clientHeight;

    // Calculate the scale ratio between the canvas dimensions and display dimensions
    const displayScaleX = displayWidth / canvasWidth;
    const displayScaleY = displayHeight / canvasHeight;

    // Clear the entire canvas
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // Set up scaling transformation to ensure proper rendering
    ctx.save();
    ctx.scale(1 / displayScaleX, 1 / displayScaleY);

    // Draw bounding box if there's a current tag
    const currentTag = filteredTags.find(
      (x) => x.frame === currentFrame && x.athleteId === selectedAthlete,
    );

    if (currentTag) {
      drawTagBoundingBox(
        ctx,
        currentTag,
        displayWidth,
        displayHeight,
        displayScaleX,
        displayScaleY,
      );
    }

    // Draw pose overlay (keypoints and skeleton) for current frame
    if (showPose && currentFramePose.keypoints.length > 0) {
      const ffmpegWidth = firstSegment?.width;
      const ffmpegHeight = firstSegment?.height;

      const videoElement = videoRef.current;
      const videoWidth = ffmpegWidth ?? videoElement?.videoWidth ?? canvasWidth;
      const videoHeight =
        ffmpegHeight ?? videoElement?.videoHeight ?? canvasHeight;

      drawPoseOverlay(
        ctx,
        currentFramePose.keypoints,
        showAngles ? currentFramePose.angles : [],
        displayWidth,
        displayHeight,
        displayScaleX,
        displayScaleY,
        videoWidth,
        videoHeight,
        poseCircleThickness,
        poseLineThickness,
        poseEditingState.isPoseEditMode,
        poseEditingState.selectedKeypoint?.keypointNum,
        modifiedKeypoints,
        currentFrame,
      );
    }

    // Draw all saved lines
    drawLines(
      ctx,
      lines,
      displayWidth,
      displayHeight,
      displayScaleX,
      displayScaleY,
      drawingMode,
    );

    // Draw the current line being drawn
    drawCurrentLine(
      ctx,
      currentLine,
      selectedColour,
      displayWidth,
      displayHeight,
      displayScaleX,
      displayScaleY,
      drawingMode,
    );

    // Restore the canvas context to its original state
    ctx.restore();
  }, [
    currentFrame,
    filteredTags,
    showPose,
    showAngles,
    lines,
    currentLine,
    currentFramePose,
    firstSegment,
    drawingMode,
    selectedColour,
    selectedAthlete,
    poseCircleThickness,
    poseLineThickness,
  ]);

  return {
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
  };
}
