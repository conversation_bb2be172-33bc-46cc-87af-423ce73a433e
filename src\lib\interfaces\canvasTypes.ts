import type { Dispatch, MouseEvent, RefObject, SetStateAction } from "react";
import { type TSSegment } from "../ffmpeg";
import type { TaglistTag } from "../interface";
import {
  type ColouredLine,
  type CurrentPose,
  type DrawingMode,
  type PoseEditingState,
  type EditableKeypoint,
} from "./drawingTypes";

// Canvas manager props
export interface CanvasManagerProps {
  canvasRef: RefObject<HTMLCanvasElement>;
  currentFrame: number;
  filteredTags: TaglistTag[];
  selectedAthlete: string;
  showPose: boolean;
  showAngles: boolean;
  currentFramePose: CurrentPose;
  firstSegment?: TSSegment;
  lines: ColouredLine[];
  setLines: Dispatch<SetStateAction<ColouredLine[]>>;
  drawingMode: DrawingMode;
  selectedColour: string;
  isCtrlPressed: boolean;
  strokeWidth: number;
  poseCircleThickness: number;
  poseLineThickness: number;
  // Pose editing props
  poseEditingState: PoseEditingState;
  setPoseEditingState: Dispatch<SetStateAction<PoseEditingState>>;
  modifiedKeypoints: Map<string, EditableKeypoint>;
  setModifiedKeypoints: Dispatch<SetStateAction<Map<string, EditableKeypoint>>>;
}

// Canvas manager return type
export interface CanvasManagerHandlers {
  handleMouseDown: (e: MouseEvent<HTMLCanvasElement>) => void;
  handleMouseMove: (e: MouseEvent<HTMLCanvasElement>) => void;
  handleMouseUp: () => void;
}
