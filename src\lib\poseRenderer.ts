import { poseConnections } from "~/lib/constants";
import { angleAnchors } from "./interfaces/drawingTypes";
import type {
  PartialBodyAngles,
  PartialBodyKeypoints,
} from "./interfaces/drawingTypes";
// Use the existing poseConnections from constants.ts
// Keypoint indices for angle calculations (based on the poseConnections structure)
const ANGLE_KEYPOINTS = {
  // Left arm: shoulder (14) -> elbow (15) -> wrist (16)
  left_arm: [14, 15, 16],
  // Right arm: shoulder (11) -> elbow (12) -> wrist (13)
  right_arm: [11, 12, 13],
  // Left leg: hip (1) -> knee (4) -> ankle (5)
  left_leg: [1, 4, 5],
  // Right leg: hip (0) -> knee (2) -> ankle (3)
  right_leg: [0, 2, 3],
};

export function calculateAngleFromThreePoints(
  p1: { x: number; y: number },
  p2: { x: number; y: number },
  p3: { x: number; y: number },
): number {
  // Calculate vectors from middle point to other points
  const v1 = { x: p1.x - p2.x, y: p1.y - p2.y };
  const v2 = { x: p3.x - p2.x, y: p3.y - p2.y };

  // Calculate dot product and magnitudes
  const dotProduct = v1.x * v2.x + v1.y * v2.y;
  const magnitude1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y);
  const magnitude2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y);

  // Avoid division by zero
  if (magnitude1 === 0 || magnitude2 === 0) return 0;

  // Calculate angle in radians, then convert to degrees
  const cosAngle = dotProduct / (magnitude1 * magnitude2);
  // Clamp to avoid floating point errors
  const clampedCos = Math.max(-1, Math.min(1, cosAngle));
  const angleRadians = Math.acos(clampedCos);
  const angleDegrees = (angleRadians * 180) / Math.PI;

  return angleDegrees;
}

export function calculateAnglesFromKeypoints(
  keypoints: PartialBodyKeypoints,
  frameNumber: number,
): PartialBodyAngles {
  const angles: PartialBodyAngles = [];

  // Helper function to find keypoint by number
  const findKeypoint = (keypointNum: number) =>
    keypoints.find((kp) => kp.keypointNum === keypointNum);

  // Calculate angles for each body part using the ANGLE_KEYPOINTS structure
  Object.entries(ANGLE_KEYPOINTS).forEach(([angleName, keypointIndices]) => {
    const [p1Idx, p2Idx, p3Idx] = keypointIndices;
    if (
      typeof p1Idx === "number" &&
      typeof p2Idx === "number" &&
      typeof p3Idx === "number"
    ) {
      const point1 = findKeypoint(p1Idx);
      const point2 = findKeypoint(p2Idx);
      const point3 = findKeypoint(p3Idx);

      if (point1 && point2 && point3) {
        const angle = calculateAngleFromThreePoints(point1, point2, point3);
        angles.push({
          frameNumber,
          name: angleName,
          angle,
          aiScore: 1.0, // Set to 1.0 for manually calculated angles
        });
      }
    }
  });

  return angles;
}

export function isClickOnKeypoint(
  mouseX: number,
  mouseY: number,
  keypoint: { x: number; y: number },
  circleRadius: number,
  displayWidth: number,
  displayHeight: number,
  videoWidth: number,
  videoHeight: number,
): boolean {
  // Convert keypoint coordinates to display coordinates
  let keypointX, keypointY;
  if (keypoint.x <= 1 && keypoint.y <= 1) {
    keypointX = keypoint.x * displayWidth;
    keypointY = keypoint.y * displayHeight;
  } else {
    const scaleToDisplayX = displayWidth / videoWidth;
    const scaleToDisplayY = displayHeight / videoHeight;
    keypointX = keypoint.x * scaleToDisplayX;
    keypointY = keypoint.y * scaleToDisplayY;
  }

  // Convert mouse coordinates to display coordinates
  const mouseDisplayX = mouseX * displayWidth;
  const mouseDisplayY = mouseY * displayHeight;

  // Calculate distance between mouse and keypoint
  const distance = Math.sqrt(
    Math.pow(mouseDisplayX - keypointX, 2) +
      Math.pow(mouseDisplayY - keypointY, 2),
  );

  return distance <= circleRadius;
}

export function findClickedKeypoint(
  mouseX: number,
  mouseY: number,
  keypoints: PartialBodyKeypoints,
  circleRadius: number,
  displayWidth: number,
  displayHeight: number,
  videoWidth: number,
  videoHeight: number,
): (PartialBodyKeypoints[0] & { keypointNum: number }) | null {
  // Check keypoints that are part of the pose connections
  const keypointNumsToDraw = new Set<number>();
  poseConnections.forEach(([fromIdx, toIdx]) => {
    if (fromIdx !== undefined) keypointNumsToDraw.add(fromIdx);
    if (toIdx !== undefined) keypointNumsToDraw.add(toIdx);
  });

  for (const keypointNum of keypointNumsToDraw) {
    const keypoint = keypoints.find((k) => k.keypointNum === keypointNum);
    if (!keypoint) continue;

    if (
      isClickOnKeypoint(
        mouseX,
        mouseY,
        keypoint,
        circleRadius,
        displayWidth,
        displayHeight,
        videoWidth,
        videoHeight,
      )
    ) {
      return { ...keypoint, keypointNum };
    }
  }

  return null;
}

export function drawPoseOverlay(
  ctx: CanvasRenderingContext2D,
  keypoints: PartialBodyKeypoints,
  angles: PartialBodyAngles,
  displayWidth: number,
  displayHeight: number,
  displayScaleX: number,
  displayScaleY: number,
  videoWidth: number,
  videoHeight: number,
  poseCircleThickness: number,
  poseLineThickness: number,
  isPoseEditMode = false,
  selectedKeypointNum?: number,
  modifiedKeypoints?: Map<
    string,
    { keypointNum: number; x: number; y: number; frameNumber: number }
  >,
  currentFrame?: number,
) {
  if (keypoints.length === 0) return;

  ctx.lineWidth = poseLineThickness / Math.min(displayScaleX, displayScaleY);

  // In edit mode, we want to show both original and modified positions
  if (isPoseEditMode && modifiedKeypoints && currentFrame !== undefined) {
    // Draw skeleton lines with original keypoints (red)
    drawSkeletonLines(
      ctx,
      keypoints,
      displayWidth,
      displayHeight,
      videoWidth,
      videoHeight,
      displayScaleX,
      displayScaleY,
      poseLineThickness,
      "original",
    );

    // Draw original keypoints (red/faded)
    drawKeypoints(
      ctx,
      keypoints,
      displayWidth,
      displayHeight,
      videoWidth,
      videoHeight,
      displayScaleX,
      displayScaleY,
      poseCircleThickness,
      isPoseEditMode,
      selectedKeypointNum,
      "original",
      modifiedKeypoints,
      currentFrame,
    );

    // Apply modified keypoints for new skeleton and keypoints
    const effectiveKeypoints = keypoints.map((kp) => {
      const modifiedKey = `${currentFrame}-${kp.keypointNum}`;
      const modified = modifiedKeypoints.get(modifiedKey);
      return modified ? { ...kp, x: modified.x, y: modified.y } : kp;
    });

    // Draw skeleton lines with modified keypoints (green)
    drawSkeletonLines(
      ctx,
      effectiveKeypoints,
      displayWidth,
      displayHeight,
      videoWidth,
      videoHeight,
      displayScaleX,
      displayScaleY,
      poseLineThickness,
      "edit",
    );

    // Draw modified keypoints (green/bright)
    drawKeypoints(
      ctx,
      effectiveKeypoints,
      displayWidth,
      displayHeight,
      videoWidth,
      videoHeight,
      displayScaleX,
      displayScaleY,
      poseCircleThickness,
      isPoseEditMode,
      selectedKeypointNum,
      "modified",
      modifiedKeypoints,
      currentFrame,
    );
  } else {
    // Normal mode - draw blue skeleton
    drawSkeletonLines(
      ctx,
      keypoints,
      displayWidth,
      displayHeight,
      videoWidth,
      videoHeight,
      displayScaleX,
      displayScaleY,
      poseLineThickness,
      isPoseEditMode ? "edit" : "normal",
    );

    drawKeypoints(
      ctx,
      keypoints,
      displayWidth,
      displayHeight,
      videoWidth,
      videoHeight,
      displayScaleX,
      displayScaleY,
      poseCircleThickness,
      isPoseEditMode,
      selectedKeypointNum,
      "normal",
    );
  }

  let effectiveAngles = angles;
  let effectiveKeypointsForAngles = keypoints;

  if (isPoseEditMode && modifiedKeypoints && currentFrame !== undefined) {
    // Apply modified keypoints for angle calculations
    effectiveKeypointsForAngles = keypoints.map((kp) => {
      const modifiedKey = `${currentFrame}-${kp.keypointNum}`;
      const modified = modifiedKeypoints.get(modifiedKey);
      return modified ? { ...kp, x: modified.x, y: modified.y } : kp;
    });

    // Calculate real-time angles from modified keypoints
    effectiveAngles = calculateAnglesFromKeypoints(
      effectiveKeypointsForAngles,
      currentFrame,
    );
  }

  drawAngleAnnotations(
    ctx,
    effectiveKeypointsForAngles,
    effectiveAngles,
    displayWidth,
    displayHeight,
    videoWidth,
    videoHeight,
    displayScaleX,
    displayScaleY,
    poseCircleThickness,
  );

  // Draw editing mode indicator
  if (isPoseEditMode) {
    const fontSize = 26 / Math.min(displayScaleX, displayScaleY);
    ctx.font = `bold ${fontSize}px Arial`;
    ctx.fillStyle = "rgba(255, 255, 0, 0.9)";
    ctx.strokeStyle = "rgba(0, 0, 0, 0.8)";
    ctx.lineWidth = 2 / Math.min(displayScaleX, displayScaleY);

    const text = "POSE EDIT MODE";
    const textWidth = ctx.measureText(text).width;
    const x = displayWidth - textWidth - 10;
    const y = 30;

    // Draw text with outline
    ctx.strokeText(text, x, y);
    ctx.fillText(text, x, y);

    // Draw legend
    const legendY = y + 25;
    const legendFontSize = 18 / Math.min(displayScaleX, displayScaleY);
    ctx.font = `${legendFontSize}px Arial`;
    ctx.fillStyle = "rgba(255, 255, 255, 0.9)";
    ctx.strokeStyle = "rgba(0, 0, 0, 0.8)";
    ctx.lineWidth = 1 / Math.min(displayScaleX, displayScaleY);

    const legends = [
      "🟢 Edit mode (Green)",
      "� Original position (Red)",
      "🟡 Selected keypoint",
    ];

    legends.forEach((legend, index) => {
      const legendText = legend;
      const legendX = displayWidth - ctx.measureText(legendText).width - 10;
      const legendYPos = legendY + index * 18;
      ctx.strokeText(legendText, legendX, legendYPos);
      ctx.fillText(legendText, legendX, legendYPos);
    });
  }
}

function drawSkeletonLines(
  ctx: CanvasRenderingContext2D,
  keypoints: PartialBodyKeypoints,
  displayWidth: number,
  displayHeight: number,
  videoWidth: number,
  videoHeight: number,
  displayScaleX: number,
  displayScaleY: number,
  poseLineThickness: number,
  mode: "normal" | "edit" | "original" = "normal",
) {
  poseConnections.forEach(([fromIdx, toIdx]) => {
    const fromKeypoint = keypoints.find((x) => x.keypointNum === fromIdx);
    const toKeypoint = keypoints.find((x) => x.keypointNum === toIdx);

    if (fromKeypoint && toKeypoint) {
      let fromX, fromY, toX, toY;

      if (fromKeypoint.x <= 1 && fromKeypoint.y <= 1) {
        fromX = fromKeypoint.x * displayWidth;
        fromY = fromKeypoint.y * displayHeight;
      } else {
        const scaleToDisplayX = displayWidth / videoWidth;
        const scaleToDisplayY = displayHeight / videoHeight;
        fromX = fromKeypoint.x * scaleToDisplayX;
        fromY = fromKeypoint.y * scaleToDisplayY;
      }

      if (toKeypoint.x <= 1 && toKeypoint.y <= 1) {
        toX = toKeypoint.x * displayWidth;
        toY = toKeypoint.y * displayHeight;
      } else {
        const scaleToDisplayX = displayWidth / videoWidth;
        const scaleToDisplayY = displayHeight / videoHeight;
        toX = toKeypoint.x * scaleToDisplayX;
        toY = toKeypoint.y * scaleToDisplayY;
      }

      ctx.beginPath();
      ctx.moveTo(fromX, fromY);
      ctx.lineTo(toX, toY);

      // Set line color based on mode
      if (mode === "original") {
        ctx.strokeStyle = "rgba(255, 0, 0, 0.6)"; // Red for original positions when modified
      } else if (mode === "edit") {
        ctx.strokeStyle = "rgb(0, 255, 0)"; // Green for current/modified skeleton in edit mode
      } else {
        ctx.strokeStyle = "rgb(0, 100, 255)"; // Blue for normal mode
      }

      ctx.stroke();
    }
  });
}

function drawKeypoints(
  ctx: CanvasRenderingContext2D,
  keypoints: PartialBodyKeypoints,
  displayWidth: number,
  displayHeight: number,
  videoWidth: number,
  videoHeight: number,
  displayScaleX: number,
  displayScaleY: number,
  poseCircleThickness: number,
  isPoseEditMode: boolean,
  selectedKeypointNum?: number,
  renderMode: "normal" | "original" | "modified" = "normal",
  modifiedKeypoints?: Map<
    string,
    { keypointNum: number; x: number; y: number; frameNumber: number }
  >,
  currentFrame?: number,
) {
  // Red circles at all keypoints
  const keypointNumsToDraw = new Set<number>();
  poseConnections.forEach(([fromIdx, toIdx]) => {
    if (fromIdx !== undefined) keypointNumsToDraw.add(fromIdx);
    if (toIdx !== undefined) keypointNumsToDraw.add(toIdx);
  });

  keypointNumsToDraw.forEach((num) => {
    const keypoint = keypoints.find((k) => k.keypointNum === num);
    if (!keypoint) return;

    let x, y;
    if (keypoint.x <= 1 && keypoint.y <= 1) {
      x = keypoint.x * displayWidth;
      y = keypoint.y * displayHeight;
    } else {
      const scaleToDisplayX = displayWidth / videoWidth;
      const scaleToDisplayY = displayHeight / videoHeight;
      x = keypoint.x * scaleToDisplayX;
      y = keypoint.y * scaleToDisplayY;
    }

    const circleRadius =
      poseCircleThickness / Math.min(displayScaleX, displayScaleY);

    // Determine keypoint appearance based on render mode and editing state
    let fillColor = "rgb(255, 0, 0)"; // Default red for keypoints
    let strokeColor = null;
    let strokeWidth = 0;

    // Check if this keypoint has been modified
    const isModified =
      modifiedKeypoints &&
      currentFrame !== undefined &&
      modifiedKeypoints.has(`${currentFrame}-${num}`);

    if (renderMode === "original") {
      // Original keypoints - show only if they haven't been modified, or show faded if they have
      if (isModified) {
        fillColor = "rgba(0, 100, 255, 0.8)"; // Blue for original position of modified keypoints (underlying)
        strokeColor = "rgba(0, 100, 255, 1.0)";
        strokeWidth = 2 / Math.min(displayScaleX, displayScaleY);
      } else {
        // Don't draw original keypoints that haven't been modified (they'll be drawn in the modified pass)
        return;
      }
    } else if (renderMode === "modified") {
      // Modified keypoints - green for new positions
      if (isModified) {
        if (selectedKeypointNum === num) {
          // Selected modified keypoint - bright yellow with thick border
          fillColor = "rgb(255, 255, 0)";
          strokeColor = "rgb(255, 165, 0)";
          strokeWidth = 3 / Math.min(displayScaleX, displayScaleY);
        } else {
          // Other modified keypoints - bright green
          fillColor = "rgb(0, 255, 0)";
          strokeColor = "rgb(0, 200, 0)";
          strokeWidth = 2 / Math.min(displayScaleX, displayScaleY);
        }
      } else {
        // Unmodified keypoints in edit mode - red
        if (selectedKeypointNum === num) {
          fillColor = "rgb(255, 255, 0)";
          strokeColor = "rgb(255, 165, 0)";
          strokeWidth = 3 / Math.min(displayScaleX, displayScaleY);
        } else {
          fillColor = "rgb(255, 0, 0)"; // Red for unmodified in edit mode
        }
      }
    } else {
      // Normal mode
      if (isPoseEditMode) {
        if (selectedKeypointNum === num) {
          fillColor = "rgb(255, 255, 0)";
          strokeColor = "rgb(255, 165, 0)";
          strokeWidth = 3 / Math.min(displayScaleX, displayScaleY);
        } else {
          fillColor = "rgb(255, 0, 0)"; // Red for edit mode
        }
      } else {
        fillColor = "rgb(255, 0, 0)"; // Red for normal mode
      }
    }

    ctx.beginPath();
    ctx.arc(x, y, circleRadius, 0, 2 * Math.PI);
    ctx.fillStyle = fillColor;
    ctx.fill();

    // Draw stroke if specified
    if (strokeColor && strokeWidth > 0) {
      ctx.beginPath();
      ctx.arc(x, y, circleRadius, 0, 2 * Math.PI);
      ctx.strokeStyle = strokeColor;
      ctx.lineWidth = strokeWidth;
      ctx.stroke();
    }
  });
}

function drawAngleAnnotations(
  ctx: CanvasRenderingContext2D,
  keypoints: PartialBodyKeypoints,
  angles: PartialBodyAngles,
  displayWidth: number,
  displayHeight: number,
  videoWidth: number,
  videoHeight: number,
  displayScaleX: number,
  displayScaleY: number,
  poseCircleThickness: number,
) {
  // Angle anchor keypoints
  angles.forEach((angle) => {
    const anchorKeypointNum = angleAnchors[angle.name];
    if (anchorKeypointNum === undefined) return;

    const keypoint = keypoints.find((k) => k.keypointNum === anchorKeypointNum);
    if (!keypoint) return;

    let x, y;
    if (keypoint.x <= 1 && keypoint.y <= 1) {
      x = keypoint.x * displayWidth;
      y = keypoint.y * displayHeight;
    } else {
      const scaleToDisplayX = displayWidth / videoWidth;
      const scaleToDisplayY = displayHeight / videoHeight;
      x = keypoint.x * scaleToDisplayX;
      y = keypoint.y * scaleToDisplayY;
    }

    const circleRadius =
      poseCircleThickness / Math.min(displayScaleX, displayScaleY);
    const fontSize = 18 / Math.min(displayScaleX, displayScaleY);
    ctx.font = `${fontSize}px Arial`;
    ctx.fillStyle = "yellow";
    ctx.fillText(`${angle.angle.toFixed(0)}`, x + circleRadius + 2, y);
  });
}
