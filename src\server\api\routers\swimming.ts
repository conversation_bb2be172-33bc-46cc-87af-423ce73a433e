import { and, asc, eq, inArray } from "drizzle-orm";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { type SwimmingTag, swimmingTags } from "~/server/db/schema";
import type { RouterInputs, RouterOutputs } from "~/trpc/react";

type Inputs = RouterInputs["swimming"];
type Outputs = RouterOutputs["swimming"];

export type FinishReviewInput = Inputs["editTagAthlete"];
export type GetSwimmingTagsOutput = Outputs["getVideoTags"];

export const swimmingRouter = createTRPCRouter({
  getVideoTags: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      const tags = await ctx.db.query.swimmingTags.findMany({
        where: and(
          eq(swimmingTags.videoId, input.id),
          eq(swimmingTags.isDeleted, false),
        ),
        orderBy: [asc(swimmingTags.frame)],
      });

      return { tags };
    }),
  upsertTag: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        videoId: z.string(),
        athleteId: z.string(),
        frame: z.number(),
        tag: z.string(),
        aiTagId: z.string().nullish(),
        x1: z.number().nullish(),
        x2: z.number().nullish(),
        y1: z.number().nullish(),
        y2: z.number().nullish(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.session.user.id;
      await ctx.db
        .insert(swimmingTags)
        .values({
          ...input,
          userId,
        })
        .onDuplicateKeyUpdate({
          set: { ...input, userId },
        });
      return {
        ...input,
        userId,
      } as SwimmingTag;
    }),
  deleteTag: protectedProcedure
    .input(z.object({ id: z.string(), softDelete: z.boolean().optional() }))
    .mutation(async ({ input, ctx }) => {
      const isSoftDelete = input.softDelete === true;
      if (isSoftDelete) {
        await ctx.db
          .update(swimmingTags)
          .set({
            isDeleted: true,
          })
          .where(eq(swimmingTags.id, input.id));
      } else {
        await ctx.db.delete(swimmingTags).where(eq(swimmingTags.id, input.id));
      }
      return input.id;
    }),
  deleteTags: protectedProcedure
    .input(
      z.object({
        softDeleteIds: z.array(z.string()),
        hardDeleteIds: z.array(z.string()),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      await ctx.db.transaction(async (tx) => {
        await tx
          .update(swimmingTags)
          .set({ isDeleted: true })
          .where(inArray(swimmingTags.id, input.softDeleteIds));
        await tx
          .delete(swimmingTags)
          .where(inArray(swimmingTags.id, input.hardDeleteIds));
      });
    }),
  deleteAthleteTags: protectedProcedure
    .input(z.object({ athleteId: z.string() }))
    .mutation(({ input, ctx }) => {
      return ctx.db
        .delete(swimmingTags)
        .where(eq(swimmingTags.athleteId, input.athleteId));
    }),
  editTagAthlete: protectedProcedure
    .input(
      z.object({
        videoId: z.string(),
        originalAthleteId: z.string(),
        newAthleteId: z.string(),
      }),
    )
    .mutation(({ input, ctx }) => {
      return ctx.db
        .update(swimmingTags)
        .set({ athleteId: input.newAthleteId })
        .where(
          and(
            eq(swimmingTags.videoId, input.videoId),
            eq(swimmingTags.athleteId, input.originalAthleteId),
          ),
        );
    }),
  // finishReview: protectedProcedure
  //   .input(
  //     z.object({
  //       videoId: z.string(),
  //       changes: z
  //         .array(
  //           z.object({
  //             id: z.string(),
  //             action: z.nativeEnum(TagAction),
  //             athleteId: z.string(),
  //             frame: z.number(),
  //             tag: z.string(),
  //           }),
  //         )
  //         .optional(),
  //     }),
  //   )
  //   .mutation(async ({ ctx, input }) => {
  //     const tagsToInsert = input.changes
  //       ?.filter((x) => x.action === TagAction.add)
  //       .map((change) => ({
  //         id: change.id,
  //         videoId: input.videoId,
  //         athleteId: change.athleteId,
  //         frame: change.frame,
  //         tag: change.tag,
  //         userId: ctx.session.user.id,
  //       }));

  //     await ctx.db.transaction(async (tx) => {
  //       if (tagsToInsert && tagsToInsert.length > 0) {
  //         await tx.insert(swimmingTags).values(tagsToInsert);
  //       }

  //       const tagsToDelete = input.changes
  //         ?.filter((x) => x.action === TagAction.remove)
  //         .map((change) => change.id);

  //       if (tagsToDelete && tagsToDelete.length > 0) {
  //         await tx
  //           .delete(swimmingTags)
  //           .where(inArray(swimmingTags.id, tagsToDelete));
  //       }

  //       const tagsToUpdate = input.changes?.filter(
  //         (x) => x.action === TagAction.update,
  //       );

  //       if (tagsToUpdate && tagsToUpdate.length > 0) {
  //         const sqlChunksFrame: SQL[] = [sql`(case`];
  //         const sqlChunksTag: SQL[] = [sql`(case`];
  //         const sqlChunksAthlete: SQL[] = [sql`(case`];
  //         const ids: string[] = [];

  //         for (const tag of tagsToUpdate) {
  //           sqlChunksFrame.push(
  //             sql`when ${swimmingTags.id} = ${tag.id} then ${tag.frame}`,
  //           );
  //           sqlChunksTag.push(
  //             sql`when ${swimmingTags.id} = ${tag.id} then ${tag.tag}`,
  //           );
  //           sqlChunksAthlete.push(
  //             sql`when ${swimmingTags.id} = ${tag.id} then ${tag.athleteId}`,
  //           );
  //           ids.push(tag.id);
  //         }

  //         sqlChunksFrame.push(sql`end)`);
  //         sqlChunksTag.push(sql`end)`);
  //         sqlChunksAthlete.push(sql`end)`);

  //         const finalSqlFrame: SQL = sql.join(sqlChunksFrame, sql.raw(" "));
  //         const finalSqlTag: SQL = sql.join(sqlChunksTag, sql.raw(" "));
  //         const finalSqlAthlete: SQL = sql.join(sqlChunksAthlete, sql.raw(" "));

  //         await tx
  //           .update(swimmingTags)
  //           .set({
  //             tag: finalSqlTag,
  //             frame: finalSqlFrame,
  //             athleteId: finalSqlAthlete,
  //             userId: ctx.session.user.id,
  //           })
  //           .where(inArray(swimmingTags.id, ids));
  //       }
  //     });

  //     await updateVideoStatus(input.videoId);

  //     const aiTaggedVideos = await getVideos({
  //       token: ctx.session.accessToken,
  //       sport: [Sport.swimming],
  //     });
  //     return aiTaggedVideos.list[0];
  //   }),
});
